"""
演示增强功能 - 不需要实际AI模型的功能演示
Demo Enhanced Features - Feature demonstration without requiring actual AI model
"""

from content_filter import ContentFilter
from explanation_handler import ExplanationHandler


def demo_content_filter():
    """演示内容过滤功能"""
    print("🛡️  内容过滤功能演示")
    print("=" * 40)
    
    filter_obj = ContentFilter()
    
    test_cases = [
        ("你真是个笨蛋！", "7-9"),
        ("我今天很开心，和小朋友一起玩游戏。", "7-9"),
        ("这个哲学问题很复杂。", "3-6"),
        ("小猫咪很可爱，我喜欢它。", "7-9"),
        ("打架是不对的行为。", "7-9")
    ]
    
    for i, (text, age_group) in enumerate(test_cases, 1):
        print(f"\n📝 测试 {i}: \"{text}\" (年龄段: {age_group})")
        result = filter_obj.comprehensive_filter(text, age_group)
        
        if result['is_safe']:
            print(f"   ✅ 内容安全")
            print(f"   📤 输出: {result['filtered_text']}")
        else:
            print(f"   ⚠️  内容不安全: {'; '.join(result['issues'])}")
            print(f"   🔄 安全回复: {result['safe_response']}")


def demo_explanation_handler():
    """演示解释处理功能"""
    print("\n\n🤔 解释处理功能演示")
    print("=" * 40)
    
    handler = ExplanationHandler()
    
    test_cases = [
        "什么是彩虹？",
        "小猫是什么？", 
        "开心是什么意思？",
        "为什么天会下雨？",
        "你好，今天天气真好。"
    ]
    
    for i, text in enumerate(test_cases, 1):
        print(f"\n📝 测试 {i}: \"{text}\"")
        is_explanation, request_type, content = handler.detect_explanation_request(text)
        
        if is_explanation:
            print(f"   ✅ 识别为解释请求")
            print(f"   📋 类型: {request_type}")
            print(f"   🎯 提取内容: {content}")
            
            if request_type == "word_explanation":
                is_common, category = handler.is_common_vocabulary(content)
                if is_common:
                    print(f"   📚 常见词汇类别: {category}")
                    quick_exp = handler.generate_quick_explanation(content, category)
                    print(f"   ⚡ 快速解释: {quick_exp}")
        else:
            print(f"   ❌ 不是解释请求，按普通对话处理")


def demo_integration():
    """演示集成处理流程"""
    print("\n\n🔄 集成处理流程演示")
    print("=" * 40)
    
    filter_obj = ContentFilter()
    handler = ExplanationHandler()
    
    test_inputs = [
        ("什么是小猫？", "7-9"),
        ("你是个笨蛋！", "7-9"),
        ("为什么天空是蓝色的？", "3-6"),
        ("我今天很开心", "7-9")
    ]
    
    for i, (user_input, age_group) in enumerate(test_inputs, 1):
        print(f"\n📝 处理 {i}: \"{user_input}\" (年龄段: {age_group})")
        
        # 步骤1: 输入内容过滤
        print("   🔍 步骤1: 检查输入安全性")
        input_filter = filter_obj.comprehensive_filter(user_input, age_group)
        
        if not input_filter['is_safe']:
            print(f"   ⚠️  输入不安全: {'; '.join(input_filter['issues'])}")
            print(f"   🔄 返回安全回复: {input_filter['safe_response']}")
            continue
        else:
            print("   ✅ 输入安全")
        
        # 步骤2: 解释检测
        print("   🔍 步骤2: 检查是否为解释请求")
        is_explanation, request_type, content = handler.detect_explanation_request(user_input)
        
        if is_explanation:
            print(f"   ✅ 识别为解释请求 - 类型: {request_type}")
            
            if request_type == "word_explanation":
                is_common, category = handler.is_common_vocabulary(content)
                if is_common:
                    print(f"   📚 常见词汇 ({category})，生成快速解释")
                    mock_response = handler.generate_quick_explanation(content, category)
                    
                    # 步骤3: 输出内容过滤
                    print("   🔍 步骤3: 检查输出安全性")
                    output_filter = filter_obj.comprehensive_filter(mock_response, age_group)
                    
                    if output_filter['is_safe']:
                        print(f"   ✅ 输出安全")
                        print(f"   📤 最终回复: {output_filter['filtered_text']}")
                    else:
                        print(f"   ⚠️  输出不安全，使用安全回复")
                else:
                    print(f"   📖 非常见词汇，需要AI生成详细解释")
            else:
                print(f"   📖 {request_type}类型，需要AI生成解释")
        else:
            print("   💬 普通对话，交给AI处理")


def main():
    """主演示函数"""
    print("🤖 儿童陪伴Agent增强功能演示")
    print("🎯 TASK001 子任务3&4: 词语解释功能 + 内容过滤机制")
    print("=" * 60)
    
    try:
        demo_content_filter()
        demo_explanation_handler()
        demo_integration()
        
        print("\n" + "=" * 60)
        print("✨ 演示完成！")
        print("\n📋 功能总结:")
        print("   ✅ 子任务3: 词语、句子解释功能 - 已实现")
        print("      - 自动识别解释类问题")
        print("      - 支持词语、句子、为什么类问题")
        print("      - 常见词汇快速解释")
        print("      - 年龄段适配的解释Prompt")
        print("\n   ✅ 子任务4: 适龄表达与内容过滤机制 - 已实现")
        print("      - 敏感词检测和过滤")
        print("      - 年龄适宜性检查")
        print("      - 友好词汇替换")
        print("      - 安全回复机制")
        print("\n🚀 可以运行 basic_child_chat_agent.py 体验完整功能！")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
