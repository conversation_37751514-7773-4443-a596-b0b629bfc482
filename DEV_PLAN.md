# DEV_PLAN.md

## 版本路线图

| 版本 | 主要特性 |
|------|----------|
| 1.0  | MVP：基础聊天、故事讲述、音乐播放、基础年龄分层 |
| 2.0  | 健康价值观引导、情感陪伴（TTS）、内容安全过滤、家长管理 |
| 3.0  | 多模态输入、多语言、成长档案、社区功能 |
| 4.0+ | AR/VR互动、智能推荐、开放API等 |

---

## 1.0 版本任务拆解

### TASK001 聊天与语言解释Agent开发

- 版本：1.0
- 状态：计划中

### 子任务清单与执行步骤

#### 1. 设计儿童友好型对话Prompt
- 明确目标用户年龄段（如3-6岁、7-9岁、10-12岁），调研儿童常用表达和兴趣点。
  - 参考儿童心理学、教育学资料，收集常见兴趣（如动物、自然现象、交通工具、童话、科学小知识、日常生活、朋友、家人等）。
- 提炼适龄表达原则：
  - 用词简单、句子短小、语气温柔、鼓励提问、积极正向、适当幽默。
  - 多用"你觉得呢？""我们一起来想一想吧！"等鼓励性结尾。
  - 避免复杂词汇、否定、批评、恐吓、成人化内容。
- 设计多轮对话场景，覆盖"为什么"类问题、日常问候、幽默互动、故事请求、情感表达等。
  - 示例场景：
    - "你好呀！今天过得开心吗？"
    - "你知道为什么天会下雨吗？其实是因为云朵里有很多小水滴，它们变重了就会掉下来哦！"
    - "你想听什么样的故事呢？有关于小兔子的，还是关于宇宙的？"
    - "你看起来有点不开心，要不要和我说说？"
    - "我会讲笑话哦，你想听吗？"
- 编写Prompt模板，反复测试并优化表达风格。
  - 通用Prompt：
    ```
    你是一位温柔的儿童陪伴AI，请用简单、温暖的语言和孩子对话，遇到"为什么"类问题要耐心解释，避免使用复杂词汇，鼓励孩子多提问。表达要积极、正向、幽默，适当引导孩子表达自己的想法。
    ```
  - 分年龄段Prompt建议：
    - 3-6岁：
      ```
      你是一位温柔的AI朋友，和3-6岁的小朋友说话时要用很简单的词语和短句，多用夸奖和鼓励，像讲故事一样和他们交流。
      ```
    - 7-9岁：
      ```
      你是一位温柔的AI朋友，和7-9岁的小朋友说话时可以用简单的逻辑和因果关系，鼓励他们多提问和表达自己的想法。
      ```
    - 10-12岁：
      ```
      你是一位温柔的AI朋友，和10-12岁的小朋友说话时可以加入一些有趣的知识和小挑战，鼓励他们思考和探索。
      ```
- 组织家长、教育专家评审Prompt内容，收集反馈。
- 通过实际对话测试，观察AI输出是否符合预期风格。
- 针对不同年龄段和场景，持续优化Prompt表达。

#### 2. 实现基础对话Agent（集成Agno Agent）
- 搭建Agno开发环境，安装依赖：
  ```bash
  pip install agno openai
  ```
- 选择合适的LLM模型（如gpt-4o），初始化Agent对象。
- 将已设计的Prompt集成到Agent的instructions参数中。
- 实现Agent的基础对话接口，支持多轮对话。
  - 支持print_response、run等方法，便于终端和API调用。
- 本地或沙盒环境下进行多轮对话测试，记录异常和改进点。
- 示例代码片段：
  ```python
  from agno.agent import Agent
  from agno.models.openai import OpenAIChat

  agent = Agent(
      model=OpenAIChat(id="gpt-4o"),
      instructions="你是一位温柔的儿童陪伴AI，请用简单、温暖的语言和孩子对话，遇到'为什么'类问题要耐心解释，避免使用复杂词汇，鼓励孩子多提问。",
      markdown=True,
  )

  # 示例对话
  agent.print_response("你好，你是谁？")
  agent.print_response("你能给我讲个小故事吗？")
  agent.print_response("为什么天会下雨？")
  ```
- 可选：集成会话历史功能，提升多轮对话连贯性。
- 可选：为后续多模态输入/输出、内容过滤等功能预留接口。
- 注意事项：
  - instructions需调用子任务1产出的Prompt模板。
  - 测试时关注AI输出是否温柔、适龄、鼓励性强。
  - 记录所有异常和不符合预期的对话，便于后续优化。

#### 3. 集成词语、句子解释功能
- 梳理常见儿童疑问词库和句子类型，收集"什么是XX""XX是什么意思"等表达。
- 设计"解释型"Prompt，区分词语解释与句子解释场景。
  - 词语解释Prompt示例：
    ```
    用儿童能理解的方式解释"XX"是什么意思，举一个简单的例子。
    ```
  - 句子解释Prompt示例：
    ```
    用儿童能理解的方式解释这句话的意思，并举例说明。
    ```
- 在Agent中实现对"解释"类输入的识别与分流（如正则匹配"什么是""是什么意思"等）。
- 可选：集成外部词典API或自定义知识库，提升解释准确性。
- 测试多种输入，确保解释内容简明、适龄。

#### 4. 适龄表达与内容过滤机制开发
- 明确内容安全与适龄表达标准，制定过滤规则（如敏感词、超龄内容）。
- 集成内容安全检测工具（如敏感词过滤、年龄分级模块）。
- 在Agent输出前后增加内容审核环节，自动拦截不当内容。
  - 示例：输出内容如包含敏感词，则替换为"这个问题我还不能回答哦，我们可以聊别的有趣的话题吗？"
- 设计异常处理流程（如输出替换、友好提示）。
- 反复测试边界场景，确保过滤机制稳定可靠。

#### 验收标准

- 能与儿童进行自然对话，表达温柔、易懂。
- 能解释常见词语、句子，回答"为什么"问题。
- 不输出不当或超龄内容。

#### 注意事项

- 所有输出需适龄、积极、正向。
- 严格内容安全过滤，防止敏感信息。

---

### TASK002 故事与音乐Agent开发

- 版本：1.0
- 状态：计划中

#### 子任务清单

1. 设计故事推荐与生成Prompt
   - Prompt示例：
     ```
     你是一位会讲故事的AI，请根据孩子的兴趣和年龄推荐或生成有趣、积极的故事，故事内容需健康、富有想象力。
     ```
2. 集成故事库、音乐库
3. 实现故事/音乐内容分级与推荐
4. 支持自定义故事生成

#### 验收标准

- 能根据年龄推荐/生成故事，播放音乐。
- 故事内容健康、积极，音乐适合儿童。
- 支持自定义故事生成。

#### 注意事项

- 故事和音乐内容需分级管理，防止不当内容。
- 保证内容多样性和趣味性。

---

### TASK003 年龄分层与用户画像

- 版本：1.0
- 状态：计划中

#### 子任务清单

1. 设计用户画像采集与年龄分层Prompt
   - Prompt示例：
     ```
     请根据用户输入信息（如年龄、兴趣）自动建立用户画像，并据此调整内容推荐和表达方式。
     ```
2. 实现用户画像采集与存储
3. 年龄分层内容适配机制开发

#### 验收标准

- 能自动采集并存储用户画像信息。
- 能根据年龄分层调整内容和表达。

#### 注意事项

- 用户数据需加密存储，保护隐私。
- 年龄分层标准需科学、合理。

---

## 2.0 版本任务拆解

### TASK004 健康价值观引导Agent

- 版本：2.0
- 状态：计划中

#### 子任务清单

1. 设计价值观引导Prompt
   - Prompt示例：
     ```
     你是一位儿童成长引导AI，请在日常对话和故事中自然融入健康、友善、积极的价值观，避免说教式表达。
     ```
2. 实现价值观内容库
3. 集成互动问答、小游戏等引导方式

#### 验收标准

- 能在对话和故事中自然融入健康价值观。
- 互动方式有趣、易于接受。

#### 注意事项

- 避免生硬说教，注重潜移默化。
- 价值观内容需经专家审核。

---

### TASK005 情感陪伴与TTS多声音

- 版本：2.0
- 状态：计划中

#### 子任务清单

1. 设计情感陪伴Prompt
   - Prompt示例：
     ```
     你是一位温柔的AI朋友，请用温暖、鼓励的语气安慰和陪伴孩子，支持多种声音风格（如男声、女声、卡通）。
     ```
2. 集成TTS语音合成接口
3. 实现多声音风格切换
4. 情绪识别与安慰机制开发

#### 验收标准

- 能根据需求切换不同声音风格。
- 能识别儿童情绪并主动安慰。

#### 注意事项

- 语音输出需温柔、易懂，避免机械感。
- 情感陪伴内容需积极正向。

---

### TASK006 内容安全与家长管理

- 版本：2.0
- 状态：计划中

#### 子任务清单

1. 设计内容安全过滤Prompt
   - Prompt示例：
     ```
     请对所有输出内容进行安全过滤，防止不当、敏感、超龄信息，支持家长端内容审核与反馈。
     ```
2. 实现多层次内容安全过滤机制
3. 开发家长端管理与监控接口

#### 验收标准

- 所有内容输出均通过安全过滤。
- 家长可查看互动记录、设置偏好。

#### 注意事项

- 内容安全机制需可扩展、易维护。
- 家长端需简洁易用，保护儿童隐私。

---

## 3.0 及以上版本任务（略）

- 多模态输入、多语言、成长档案、社区功能、AR/VR互动、智能推荐、开放API等，后续版本按需细化。

---

## 计划管理

- 所有任务按敏捷开发流程推进，状态实时更新。
- 任务文档结构清晰，便于团队协作和AI自动化开发。
- 每个TASK均有详细Prompt、验收标准和注意事项，确保高质量交付。 