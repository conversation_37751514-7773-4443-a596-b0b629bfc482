"""
测试增强功能 - 词语解释和内容过滤
Test Enhanced Features - Word Explanation and Content Filtering
"""

from content_filter import ContentFilter
from explanation_handler import ExplanationHandler


def test_content_filter():
    """测试内容过滤功能"""
    print("=== 内容过滤功能测试 ===\n")
    
    filter_obj = ContentFilter()
    
    test_cases = [
        ("你真是个笨蛋！", "7-9"),
        ("我今天很开心，和小朋友一起玩游戏。", "7-9"),
        ("这个哲学问题很复杂。", "3-6"),
        ("小猫咪很可爱，我喜欢它。", "7-9"),
        ("打架是不对的行为。", "7-9"),
        ("我觉得你很愚蠢。", "10-12"),
        ("今天天气真好，我们去公园玩吧！", "3-6")
    ]
    
    for i, (text, age_group) in enumerate(test_cases, 1):
        print(f"测试 {i}: {text} (年龄段: {age_group})")
        result = filter_obj.comprehensive_filter(text, age_group)
        
        print(f"  安全性: {'✓ 安全' if result['is_safe'] else '✗ 不安全'}")
        if result['issues']:
            print(f"  问题: {'; '.join(result['issues'])}")
        print(f"  过滤后: {result['filtered_text']}")
        if result['safe_response']:
            print(f"  安全回复: {result['safe_response']}")
        print()


def test_explanation_handler():
    """测试解释处理功能"""
    print("=== 解释处理功能测试 ===\n")
    
    handler = ExplanationHandler()
    
    test_cases = [
        "什么是彩虹？",
        "小猫是什么？", 
        "开心是什么意思？",
        "为什么天会下雨？",
        "这句话是什么意思？",
        "你好，今天天气真好。",
        "太阳是什么？",
        "为什么小鸟会飞？",
        "爱是什么意思？",
        "解释一下友谊。"
    ]
    
    for i, text in enumerate(test_cases, 1):
        print(f"测试 {i}: {text}")
        is_explanation, request_type, content = handler.detect_explanation_request(text)
        
        print(f"  是否为解释请求: {'✓' if is_explanation else '✗'}")
        
        if is_explanation:
            print(f"  请求类型: {request_type}")
            print(f"  提取内容: {content}")
            
            if request_type == "word_explanation":
                is_common, category = handler.is_common_vocabulary(content)
                if is_common:
                    print(f"  常见词汇类别: {category}")
                    quick_exp = handler.generate_quick_explanation(content, category)
                    print(f"  快速解释: {quick_exp}")
            
            # 生成prompt示例（截取前100字符）
            prompt = handler.get_explanation_prompt(request_type, content, "7-9")
            print(f"  生成Prompt预览: {prompt[:100]}...")
        
        print()


def test_integration():
    """测试集成功能"""
    print("=== 集成功能测试 ===\n")
    
    filter_obj = ContentFilter()
    handler = ExplanationHandler()
    
    # 模拟用户输入处理流程
    test_inputs = [
        ("什么是小猫？", "7-9"),
        ("你是个笨蛋！", "7-9"),
        ("为什么天空是蓝色的？", "3-6"),
        ("这个哲学概念很复杂", "3-6"),
        ("我今天很开心", "7-9")
    ]
    
    for i, (user_input, age_group) in enumerate(test_inputs, 1):
        print(f"处理 {i}: {user_input} (年龄段: {age_group})")
        
        # 1. 输入内容过滤
        input_filter = filter_obj.comprehensive_filter(user_input, age_group)
        print(f"  输入安全性: {'✓' if input_filter['is_safe'] else '✗'}")
        
        if not input_filter['is_safe']:
            print(f"  安全回复: {input_filter['safe_response']}")
            print()
            continue
        
        # 2. 解释检测
        is_explanation, request_type, content = handler.detect_explanation_request(user_input)
        print(f"  解释请求: {'✓' if is_explanation else '✗'}")
        
        if is_explanation:
            print(f"  类型: {request_type}, 内容: {content}")
            
            # 3. 生成回复（模拟）
            if request_type == "word_explanation":
                is_common, category = handler.is_common_vocabulary(content)
                if is_common:
                    mock_response = handler.generate_quick_explanation(content, category)
                    print(f"  模拟回复: {mock_response}")
                    
                    # 4. 输出内容过滤
                    output_filter = filter_obj.comprehensive_filter(mock_response, age_group)
                    print(f"  输出安全性: {'✓' if output_filter['is_safe'] else '✗'}")
                    print(f"  最终回复: {output_filter['filtered_text']}")
        
        print()


def main():
    """主测试函数"""
    print("儿童陪伴Agent增强功能测试\n")
    print("=" * 50)
    
    try:
        test_content_filter()
        print("=" * 50)
        test_explanation_handler()
        print("=" * 50)
        test_integration()
        print("=" * 50)
        print("✓ 所有测试完成！")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
