from agno.agent import Agent
from agno.models.openai.like import OpenAILike

# 通用Prompt
PROMPT_GENERAL = (
    "你是一位温柔的儿童陪伴AI，请用简单、温暖的语言和孩子对话，"
    "遇到'为什么'类问题要耐心解释，避免使用复杂词汇，鼓励孩子多提问。"
    "表达要积极、正向、幽默，适当引导孩子表达自己的想法。"
)

# 各年龄段补充Prompt
PROMPT_3_6 = (
    "和3-6岁的小朋友说话时要用很简单的词语和短句，多用夸奖和鼓励，像讲故事一样和他们交流。"
)
PROMPT_7_9 = (
    "和7-9岁的小朋友说话时可以用简单的逻辑和因果关系，鼓励他们多提问和表达自己的想法。"
)
PROMPT_10_12 = (
    "和10-12岁的小朋友说话时可以加入一些有趣的知识和小挑战，鼓励他们思考和探索。"
)
PROMPT_DEFAULT = ""


def get_age_prompt(age):
    try:
        age = int(age)
    except Exception:
        return PROMPT_DEFAULT
    if 3 <= age <= 6:
        return PROMPT_3_6
    elif 7 <= age <= 9:
        return PROMPT_7_9
    elif 10 <= age <= 12:
        return PROMPT_10_12
    else:
        return PROMPT_DEFAULT


def select_prompt():
    print("请输入儿童的年龄（如5、8、11）：")
    age = input("年龄：").strip()
    age_prompt = get_age_prompt(age)
    return PROMPT_GENERAL + age_prompt

if __name__ == "__main__":
    prompt = select_prompt()
    agent = Agent(
        model=OpenAILike(
            id="bot-20250528110319-bls5q",
            api_key="ae7bbf60-d7e6-4063-9d9f-a9051ae4e2ad",
            base_url="https://ark.cn-beijing.volces.com/api/v3/bots"
        ),
        instructions=prompt,
        markdown=True,
    )
    print("儿童陪伴Agent已启动，输入你的问题吧（输入exit退出）：")
    while True:
        user_input = input("你：")
        if user_input.strip().lower() == "exit":
            print("再见！")
            break
        agent.print_response(user_input) 