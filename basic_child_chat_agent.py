from agno.agent import Agent
from agno.models.openai.like import OpenAILike
from content_filter import ContentFilter
from explanation_handler import ExplanationHandler

# 通用Prompt
PROMPT_GENERAL = (
    "你是一位温柔的儿童陪伴AI，请用简单、温暖的语言和孩子对话，"
    "遇到'为什么'类问题要耐心解释，避免使用复杂词汇，鼓励孩子多提问。"
    "表达要积极、正向、幽默，适当引导孩子表达自己的想法。"
)

# 各年龄段补充Prompt
PROMPT_3_6 = (
    "和3-6岁的小朋友说话时要用很简单的词语和短句，多用夸奖和鼓励，像讲故事一样和他们交流。"
)
PROMPT_7_9 = (
    "和7-9岁的小朋友说话时可以用简单的逻辑和因果关系，鼓励他们多提问和表达自己的想法。"
)
PROMPT_10_12 = (
    "和10-12岁的小朋友说话时可以加入一些有趣的知识和小挑战，鼓励他们思考和探索。"
)
PROMPT_DEFAULT = ""


def get_age_prompt(age):
    try:
        age = int(age)
    except Exception:
        return PROMPT_DEFAULT
    if 3 <= age <= 6:
        return PROMPT_3_6
    elif 7 <= age <= 9:
        return PROMPT_7_9
    elif 10 <= age <= 12:
        return PROMPT_10_12
    else:
        return PROMPT_DEFAULT


def get_age_group_from_age(age):
    """根据年龄获取年龄段分组"""
    try:
        age = int(age)
    except Exception:
        return "7-9"  # 默认年龄段

    if 3 <= age <= 6:
        return "3-6"
    elif 7 <= age <= 9:
        return "7-9"
    elif 10 <= age <= 12:
        return "10-12"
    else:
        return "7-9"  # 默认年龄段


class EnhancedChildChatAgent:
    """增强版儿童聊天Agent，集成解释功能和内容过滤"""

    def __init__(self, age: str):
        self.age = age
        self.age_group = get_age_group_from_age(age)
        self.content_filter = ContentFilter()
        self.explanation_handler = ExplanationHandler()

        # 构建完整的prompt
        base_prompt = PROMPT_GENERAL + get_age_prompt(age)

        # 初始化Agent
        self.agent = Agent(
            model=OpenAILike(
                id="bot-20250528110319-bls5q",
                api_key="ae7bbf60-d7e6-4063-9d9f-a9051ae4e2ad",
                base_url="https://ark.cn-beijing.volces.com/api/v3/bots"
            ),
            instructions=base_prompt,
            markdown=True,
        )

    def process_user_input(self, user_input: str) -> str:
        """
        处理用户输入，包括解释检测和内容过滤

        Args:
            user_input: 用户输入的文本

        Returns:
            str: 处理后的回复
        """
        # 1. 首先检查用户输入是否安全
        input_filter_result = self.content_filter.comprehensive_filter(user_input, self.age_group)
        if not input_filter_result["is_safe"]:
            return input_filter_result["safe_response"]

        # 2. 检查是否为解释类请求
        is_explanation, request_type, content = self.explanation_handler.detect_explanation_request(user_input)

        if is_explanation:
            # 生成专门的解释prompt
            explanation_prompt = self.explanation_handler.get_explanation_prompt(
                request_type, content, self.age_group
            )

            # 如果是常见词汇，可以提供快速解释
            if request_type == "word_explanation":
                is_common, category = self.explanation_handler.is_common_vocabulary(content)
                if is_common:
                    quick_explanation = self.explanation_handler.generate_quick_explanation(content, category)
                    # 对快速解释也进行内容过滤
                    filter_result = self.content_filter.comprehensive_filter(quick_explanation, self.age_group)
                    if filter_result["is_safe"]:
                        return filter_result["filtered_text"]

            # 使用解释prompt获取AI回复
            try:
                # 创建临时agent用于解释
                explanation_agent = Agent(
                    model=self.agent.model,
                    instructions=explanation_prompt,
                    markdown=True,
                )
                response = explanation_agent.run(user_input)
                response_text = response.content if hasattr(response, 'content') else str(response)
            except Exception as e:
                print(f"解释处理出错: {e}")
                response_text = "抱歉，我现在有点困惑，你能换个方式问我吗？"
        else:
            # 普通对话
            try:
                response = self.agent.run(user_input)
                response_text = response.content if hasattr(response, 'content') else str(response)
            except Exception as e:
                print(f"对话处理出错: {e}")
                response_text = "抱歉，我现在有点困惑，你能再说一遍吗？"

        # 3. 对AI回复进行内容过滤
        output_filter_result = self.content_filter.comprehensive_filter(response_text, self.age_group)

        if output_filter_result["is_safe"]:
            return output_filter_result["filtered_text"]
        else:
            return output_filter_result["safe_response"]

    def print_response(self, user_input: str):
        """打印处理后的回复"""
        response = self.process_user_input(user_input)
        print(f"AI小伙伴: {response}")

    def start_chat(self):
        """启动聊天循环"""
        print(f"儿童陪伴Agent已启动（年龄: {self.age}岁，年龄段: {self.age_group}）")
        print("输入你的问题吧（输入exit退出）：")
        print("提示：你可以问我'什么是XX'、'为什么XX'等问题哦！")

        while True:
            user_input = input("你：").strip()
            if user_input.lower() == "exit":
                print("AI小伙伴: 再见！希望我们下次还能一起聊天！")
                break
            elif user_input == "":
                print("AI小伙伴: 你想和我说什么呢？")
                continue

            self.print_response(user_input)


def select_prompt():
    """保持向后兼容的函数"""
    print("请输入儿童的年龄（如5、8、11）：")
    age = input("年龄：").strip()
    age_prompt = get_age_prompt(age)
    return PROMPT_GENERAL + age_prompt


if __name__ == "__main__":
    print("=== 儿童陪伴Agent启动 ===")
    print("请输入儿童的年龄（如5、8、11）：")
    age = input("年龄：").strip()

    try:
        # 创建增强版Agent
        enhanced_agent = EnhancedChildChatAgent(age)
        enhanced_agent.start_chat()
    except KeyboardInterrupt:
        print("\n\nAI小伙伴: 再见！")
    except Exception as e:
        print(f"启动出错: {e}")
        print("正在使用基础模式...")

        # 回退到基础模式
        prompt = PROMPT_GENERAL + get_age_prompt(age)
        agent = Agent(
            model=OpenAILike(
                id="bot-20250528110319-bls5q",
                api_key="ae7bbf60-d7e6-4063-9d9f-a9051ae4e2ad",
                base_url="https://ark.cn-beijing.volces.com/api/v3/bots"
            ),
            instructions=prompt,
            markdown=True,
        )
        print("基础儿童陪伴Agent已启动，输入你的问题吧（输入exit退出）：")
        while True:
            user_input = input("你：")
            if user_input.strip().lower() == "exit":
                print("再见！")
                break
            agent.print_response(user_input)