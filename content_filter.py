"""
内容过滤模块 - 用于儿童内容安全过滤和适龄表达检查
Content Filter Module - For child content safety filtering and age-appropriate expression checking
"""

import re
from typing import List, Dict, Tuple


class ContentFilter:
    """内容过滤器类，负责检测和过滤不适合儿童的内容"""
    
    def __init__(self):
        # 敏感词库 - 可以根据需要扩展
        self.sensitive_words = {
            # 暴力相关
            "violence": ["打架", "暴力", "杀", "死", "血", "伤害", "攻击", "武器", "刀", "枪"],
            # 恐怖相关
            "scary": ["恐怖", "害怕", "鬼", "魔鬼", "噩梦", "可怕", "吓人", "黑暗"],
            # 不当内容
            "inappropriate": ["笨蛋", "傻瓜", "坏蛋", "讨厌", "愚蠢", "白痴"],
            # 成人话题
            "adult_topics": ["恋爱", "结婚", "离婚", "工作", "赚钱", "政治", "宗教"]
        }
        
        # 年龄分级词汇 - 超出年龄段的复杂词汇
        self.age_inappropriate_words = {
            "3-6": ["复杂", "困难", "抽象", "理论", "概念", "哲学", "科学", "技术"],
            "7-9": ["哲学", "抽象", "理论", "复杂概念", "高级数学"],
            "10-12": ["成人话题", "复杂理论"]
        }
        
        # 友好替换词库
        self.friendly_replacements = {
            "笨蛋": "小糊涂",
            "傻瓜": "小马虎", 
            "坏蛋": "调皮鬼",
            "讨厌": "不太喜欢",
            "愚蠢": "还需要学习",
            "白痴": "小迷糊"
        }
        
        # 默认安全回复
        self.safe_responses = [
            "这个问题我还不能回答哦，我们可以聊别的有趣的话题吗？",
            "让我们聊一些更有趣的事情吧！你想听故事吗？",
            "这个话题有点复杂呢，我们换个简单有趣的话题好吗？",
            "我觉得我们可以聊一些更开心的事情！你今天做了什么有趣的事吗？"
        ]
    
    def check_sensitive_content(self, text: str) -> Tuple[bool, List[str]]:
        """
        检查文本是否包含敏感内容
        
        Args:
            text: 要检查的文本
            
        Returns:
            Tuple[bool, List[str]]: (是否包含敏感内容, 检测到的敏感词列表)
        """
        detected_words = []
        text_lower = text.lower()
        
        for category, words in self.sensitive_words.items():
            for word in words:
                if word in text_lower:
                    detected_words.append(word)
        
        return len(detected_words) > 0, detected_words
    
    def check_age_appropriateness(self, text: str, age_group: str) -> Tuple[bool, List[str]]:
        """
        检查内容是否适合指定年龄段
        
        Args:
            text: 要检查的文本
            age_group: 年龄段 ("3-6", "7-9", "10-12")
            
        Returns:
            Tuple[bool, List[str]]: (是否适龄, 检测到的不适龄词汇列表)
        """
        if age_group not in self.age_inappropriate_words:
            return True, []
        
        inappropriate_words = []
        text_lower = text.lower()
        
        for word in self.age_inappropriate_words[age_group]:
            if word in text_lower:
                inappropriate_words.append(word)
        
        return len(inappropriate_words) == 0, inappropriate_words
    
    def filter_and_replace(self, text: str) -> str:
        """
        过滤并替换文本中的不当词汇
        
        Args:
            text: 原始文本
            
        Returns:
            str: 过滤后的文本
        """
        filtered_text = text
        
        for inappropriate, friendly in self.friendly_replacements.items():
            filtered_text = re.sub(
                re.escape(inappropriate), 
                friendly, 
                filtered_text, 
                flags=re.IGNORECASE
            )
        
        return filtered_text
    
    def get_safe_response(self, index: int = 0) -> str:
        """
        获取安全的默认回复
        
        Args:
            index: 回复索引，如果超出范围则返回第一个
            
        Returns:
            str: 安全回复文本
        """
        if 0 <= index < len(self.safe_responses):
            return self.safe_responses[index]
        return self.safe_responses[0]
    
    def comprehensive_filter(self, text: str, age_group: str = "7-9") -> Dict:
        """
        综合内容过滤检查
        
        Args:
            text: 要检查的文本
            age_group: 年龄段
            
        Returns:
            Dict: 包含过滤结果的字典
        """
        result = {
            "original_text": text,
            "is_safe": True,
            "filtered_text": text,
            "issues": [],
            "safe_response": None
        }
        
        # 检查敏感内容
        has_sensitive, sensitive_words = self.check_sensitive_content(text)
        if has_sensitive:
            result["is_safe"] = False
            result["issues"].append(f"包含敏感词汇: {', '.join(sensitive_words)}")
            result["safe_response"] = self.get_safe_response(0)
        
        # 检查年龄适宜性
        is_age_appropriate, inappropriate_words = self.check_age_appropriateness(text, age_group)
        if not is_age_appropriate:
            result["is_safe"] = False
            result["issues"].append(f"包含超龄词汇: {', '.join(inappropriate_words)}")
            if not result["safe_response"]:
                result["safe_response"] = self.get_safe_response(2)
        
        # 如果内容安全，进行友好替换
        if result["is_safe"]:
            result["filtered_text"] = self.filter_and_replace(text)
        
        return result


# 测试代码
if __name__ == "__main__":
    filter_obj = ContentFilter()
    
    # 测试用例
    test_cases = [
        "你真是个笨蛋！",
        "我今天很开心，和小朋友一起玩游戏。",
        "这个哲学问题很复杂。",
        "小猫咪很可爱，我喜欢它。",
        "打架是不对的行为。"
    ]
    
    print("=== 内容过滤测试 ===")
    for i, text in enumerate(test_cases, 1):
        print(f"\n测试 {i}: {text}")
        result = filter_obj.comprehensive_filter(text, "7-9")
        print(f"安全性: {'✓' if result['is_safe'] else '✗'}")
        if result['issues']:
            print(f"问题: {'; '.join(result['issues'])}")
        print(f"过滤后: {result['filtered_text']}")
        if result['safe_response']:
            print(f"安全回复: {result['safe_response']}")
