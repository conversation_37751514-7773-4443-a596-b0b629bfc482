"""
词语和句子解释处理模块 - 专门处理儿童的"什么是"和"是什么意思"类问题
Explanation Handler Module - Specialized for handling children's "what is" and "what does it mean" questions
"""

import re
from typing import Tuple


class ExplanationHandler:
    """词语和句子解释处理器"""
    
    def __init__(self):
        # 解释类问题的识别模式
        self.explanation_patterns = [
            # 词语解释模式
            r"什么是(.+?)[\?？]?$",
            r"(.+?)是什么[\?？]?$", 
            r"(.+?)是什么意思[\?？]?$",
            r"(.+?)的意思是什么[\?？]?$",
            r"(.+?)怎么理解[\?？]?$",
            r"解释一下(.+?)[\?？]?$",
            r"(.+?)指的是什么[\?？]?$",
            
            # 句子解释模式
            r"这句话是什么意思[\?？]?",
            r"这句话怎么理解[\?？]?",
            r"这个句子的意思是[\?？]?",
            r"解释这句话[\?？]?",
        ]
        
        # 词语解释Prompt模板
        self.word_explanation_prompt = """
你现在要用儿童能理解的方式解释词语"{word}"的意思。请遵循以下要求：

1. 用简单、生动的语言解释
2. 举一个贴近儿童生活的例子
3. 语气要温柔、鼓励
4. 避免使用复杂的专业术语
5. 可以用比喻或故事的方式来解释

请用这样的格式回答：
"{word}"的意思是：[简单解释]
举个例子：[生活化的例子]
你明白了吗？还有什么想问的吗？
"""

        # 句子解释Prompt模板  
        self.sentence_explanation_prompt = """
你现在要用儿童能理解的方式解释这句话的意思："{sentence}"

请遵循以下要求：
1. 把句子分解成简单的部分来解释
2. 用孩子熟悉的词汇和概念
3. 举例说明句子想表达的意思
4. 语气要温柔、耐心
5. 鼓励孩子提问

请用这样的格式回答：
这句话的意思是：[简单解释]
换个说法就是：[更简单的表达]
举个例子：[具体例子]
你理解了吗？
"""

        # "为什么"类问题的Prompt模板
        self.why_question_prompt = """
你现在要回答一个小朋友的"为什么"问题："{question}"

请遵循以下要求：
1. 用简单、有趣的方式解释原因
2. 可以用故事、比喻或拟人的方法
3. 分步骤解释，不要一次说太多
4. 语气要温柔、充满好奇心
5. 鼓励孩子继续提问和思考

请用这样的格式回答：
这是一个很棒的问题！让我来告诉你：
[分步骤的简单解释]
你觉得有趣吗？还想知道什么呢？
"""

        # 常见儿童词汇库（用于快速识别和预设回答）
        self.common_child_vocabulary = {
            "动物": ["小猫", "小狗", "小鸟", "小鱼", "大象", "狮子", "老虎", "熊猫"],
            "自然": ["太阳", "月亮", "星星", "云朵", "雨", "雪", "风", "彩虹"],
            "交通": ["汽车", "火车", "飞机", "船", "自行车", "公交车"],
            "食物": ["苹果", "香蕉", "面包", "牛奶", "蛋糕", "糖果"],
            "家庭": ["爸爸", "妈妈", "爷爷", "奶奶", "哥哥", "姐姐", "弟弟", "妹妹"],
            "情感": ["开心", "难过", "生气", "害怕", "惊讶", "喜欢", "爱"]
        }
    
    def detect_explanation_request(self, text: str) -> Tuple[bool, str, str]:
        """
        检测是否为解释类请求
        
        Args:
            text: 用户输入的文本
            
        Returns:
            Tuple[bool, str, str]: (是否为解释请求, 请求类型, 提取的关键词/句子)
        """
        text = text.strip()
        
        # 检查"为什么"类问题
        if "为什么" in text:
            return True, "why_question", text
        
        # 检查词语解释模式
        for pattern in self.explanation_patterns[:7]:  # 前7个是词语解释模式
            match = re.search(pattern, text)
            if match:
                word = match.group(1).strip()
                return True, "word_explanation", word
        
        # 检查句子解释模式
        for pattern in self.explanation_patterns[7:]:  # 后面的是句子解释模式
            if re.search(pattern, text):
                return True, "sentence_explanation", text
        
        return False, "", ""
    
    def get_explanation_prompt(self, request_type: str, content: str, age_group: str = "7-9") -> str:
        """
        根据请求类型和内容生成相应的解释Prompt
        
        Args:
            request_type: 请求类型 ("word_explanation", "sentence_explanation", "why_question")
            content: 要解释的词语或句子
            age_group: 年龄段
            
        Returns:
            str: 生成的Prompt
        """
        if request_type == "word_explanation":
            prompt = self.word_explanation_prompt.format(word=content)
        elif request_type == "sentence_explanation":
            prompt = self.sentence_explanation_prompt.format(sentence=content)
        elif request_type == "why_question":
            prompt = self.why_question_prompt.format(question=content)
        else:
            return ""
        
        # 根据年龄段调整Prompt
        age_adjustment = self._get_age_adjustment(age_group)
        return prompt + age_adjustment
    
    def _get_age_adjustment(self, age_group: str) -> str:
        """根据年龄段获取Prompt调整内容"""
        adjustments = {
            "3-6": "\n\n特别注意：这是3-6岁的小朋友，请用最简单的词语，多用拟人和故事的方式解释。",
            "7-9": "\n\n特别注意：这是7-9岁的小朋友，可以用简单的逻辑关系，但要保持生动有趣。",
            "10-12": "\n\n特别注意：这是10-12岁的小朋友，可以加入一些有趣的知识点，鼓励思考。"
        }
        return adjustments.get(age_group, "")
    
    def is_common_vocabulary(self, word: str) -> Tuple[bool, str]:
        """
        检查是否为常见儿童词汇
        
        Args:
            word: 要检查的词语
            
        Returns:
            Tuple[bool, str]: (是否为常见词汇, 所属类别)
        """
        for category, words in self.common_child_vocabulary.items():
            if word in words:
                return True, category
        return False, ""
    
    def generate_quick_explanation(self, word: str, category: str) -> str:
        """
        为常见词汇生成快速解释
        
        Args:
            word: 词语
            category: 词汇类别
            
        Returns:
            str: 快速解释文本
        """
        quick_explanations = {
            "动物": f"{word}是一种可爱的小动物，它们有自己的家，也有自己喜欢吃的食物。你见过{word}吗？",
            "自然": f"{word}是大自然中很美丽的东西，我们经常能在天空中或者外面看到它。你喜欢{word}吗？",
            "交通": f"{word}是一种交通工具，可以帮助人们从一个地方到另一个地方。你坐过{word}吗？",
            "食物": f"{word}是一种好吃的食物，很多小朋友都喜欢吃。你喜欢吃{word}吗？",
            "家庭": f"{word}是家里很重要的人，他们很爱你，也会照顾你。你的{word}对你好吗？",
            "情感": f"{word}是一种感觉，每个人都会有这样的感觉。你什么时候会感到{word}呢？"
        }
        return quick_explanations.get(category, f"{word}是一个很有趣的词语，让我来给你解释一下它的意思。")


# 测试代码
if __name__ == "__main__":
    handler = ExplanationHandler()
    
    # 测试用例
    test_cases = [
        "什么是彩虹？",
        "小猫是什么？",
        "开心是什么意思？",
        "为什么天会下雨？",
        "这句话是什么意思？",
        "你好，今天天气真好。"
    ]
    
    print("=== 解释处理器测试 ===")
    for i, text in enumerate(test_cases, 1):
        print(f"\n测试 {i}: {text}")
        is_explanation, request_type, content = handler.detect_explanation_request(text)
        print(f"是否为解释请求: {'✓' if is_explanation else '✗'}")
        if is_explanation:
            print(f"请求类型: {request_type}")
            print(f"提取内容: {content}")
            
            if request_type == "word_explanation":
                is_common, category = handler.is_common_vocabulary(content)
                if is_common:
                    print(f"常见词汇类别: {category}")
                    quick_exp = handler.generate_quick_explanation(content, category)
                    print(f"快速解释: {quick_exp}")
            
            prompt = handler.get_explanation_prompt(request_type, content, "7-9")
            print(f"生成的Prompt长度: {len(prompt)} 字符")
